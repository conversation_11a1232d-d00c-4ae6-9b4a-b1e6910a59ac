<template>
  <div class="learning-admin">
    <!-- 页面标题 -->
    <div class="admin-header">
      <h1 class="admin-title">
        <span class="icon">⚙️</span>
        学习记录管理
      </h1>
    </div>

    <!-- 认证检查 -->
    <div v-if="!isAuthenticated" class="auth-container">
      <div class="auth-card">
        <h2>管理员认证</h2>
        <p>请输入管理员密码以访问学习记录管理功能</p>
        <form @submit.prevent="authenticate" class="auth-form">
          <input
            v-model="password"
            type="password"
            placeholder="管理员密码"
            class="auth-input"
            required
          />
          <button type="submit" class="auth-btn" :disabled="authenticating">
            {{ authenticating ? "验证中..." : "登录" }}
          </button>
        </form>
        <p v-if="authError" class="auth-error">{{ authError }}</p>
      </div>
    </div>

    <!-- 管理界面 -->
    <div v-else class="admin-content">
      <!-- 管理界面头部 -->
      <div class="admin-content-header">
        <div class="header-actions">
          <RouterLink to="/admin" class="admin-nav-btn">
            <span class="btn-icon">🔖</span>
            书签管理
          </RouterLink>
          <button @click="showAddForm = true" class="add-btn">
            <span class="btn-icon">➕</span>
            添加学习记录
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载学习记录中...</p>
      </div>

      <!-- 学习记录列表 -->
      <div v-else class="entries-management">
        <div v-if="learningEntries.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>暂无学习记录</h3>
          <p>点击上方按钮添加第一条学习记录</p>
        </div>

        <div v-else class="entries-table">
          <div class="table-header">
            <div class="header-cell">日期</div>
            <div class="header-cell">标题</div>
            <div class="header-cell">语言</div>
            <div class="header-cell">时长</div>
            <div class="header-cell">操作</div>
          </div>

          <div v-for="entry in learningEntries" :key="entry.id" class="table-row">
            <div class="table-cell">{{ formatDate(entry.date) }}</div>
            <div class="table-cell">{{ entry.title }}</div>
            <div class="table-cell">
              <span
                class="language-tag"
                :style="{ backgroundColor: getLanguageColor(entry.language) }"
              >
                {{ entry.language }}
              </span>
            </div>
            <div class="table-cell">{{ entry.duration }}</div>
            <div class="table-cell actions">
              <button @click="editEntry(entry)" class="edit-btn">编辑</button>
              <button @click="deleteEntry(entry.id)" class="delete-btn">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑表单模态框 -->
    <div v-if="showAddForm || showEditForm" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ showEditForm ? "编辑学习记录" : "添加学习记录" }}</h2>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>

        <form @submit.prevent="submitForm" class="entry-form">
          <div class="form-group">
            <label for="date">学习日期</label>
            <input id="date" v-model="formData.date" type="date" class="form-input" required />
          </div>

          <div class="form-group">
            <label for="title">学习标题</label>
            <input
              id="title"
              v-model="formData.title"
              type="text"
              class="form-input"
              placeholder="例如：Vue 3 组合式 API 学习"
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="language">编程语言</label>
              <select id="language" v-model="formData.language" class="form-input" required>
                <option value="">请选择语言</option>
                <option v-for="lang in programmingLanguages" :key="lang" :value="lang">
                  {{ lang }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="duration">学习时长</label>
              <input
                id="duration"
                v-model="formData.duration"
                type="text"
                class="form-input"
                placeholder="例如：2小时30分钟"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label for="content">学习内容</label>
            <textarea
              id="content"
              v-model="formData.content"
              class="form-textarea"
              placeholder="详细描述今天学习的内容、遇到的问题、解决方案等..."
              rows="6"
              required
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="button" @click="closeModal" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="submitting">
              {{ submitting ? "保存中..." : showEditForm ? "更新" : "添加" }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteConfirm" class="modal-overlay" @click="showDeleteConfirm = false">
      <div class="modal-content delete-modal" @click.stop>
        <div class="modal-header">
          <h2>确认删除</h2>
        </div>
        <div class="modal-body">
          <p>确定要删除这条学习记录吗？此操作无法撤销。</p>
        </div>
        <div class="form-actions">
          <button @click="showDeleteConfirm = false" class="cancel-btn">取消</button>
          <button @click="confirmDelete" class="delete-btn" :disabled="deleting">
            {{ deleting ? "删除中..." : "确认删除" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { RouterLink } from "vue-router";
import {
  learningService,
  bookmarkService,
  type LearningEntry,
  type LearningEntryInput,
} from "@/services/bookmarkService";

// 认证相关
const isAuthenticated = ref(false);
const password = ref("");
const authenticating = ref(false);
const authError = ref("");

// 数据相关
const learningEntries = ref<LearningEntry[]>([]);
const loading = ref(true);

// 表单相关
const showAddForm = ref(false);
const showEditForm = ref(false);
const submitting = ref(false);
const editingEntry = ref<LearningEntry | null>(null);

// 删除相关
const showDeleteConfirm = ref(false);
const deleting = ref(false);
const deletingId = ref("");

// 表单数据
const formData = reactive<LearningEntryInput>({
  date: "",
  title: "",
  language: "",
  duration: "",
  content: "",
});

// 编程语言选项
const programmingLanguages = [
  "JavaScript",
  "TypeScript",
  "Python",
  "Java",
  "C++",
  "C#",
  "Go",
  "Rust",
  "PHP",
  "Ruby",
  "Swift",
  "Kotlin",
  "Vue",
  "React",
  "Angular",
  "Node.js",
  "HTML",
  "CSS",
  "SCSS",
  "SQL",
  "MongoDB",
  "Docker",
  "Git",
  "Linux",
  "AWS",
  "Azure",
];

// 认证
const authenticate = async () => {
  authenticating.value = true;
  authError.value = "";

  try {
    const isValid = await bookmarkService.verifyAdminPassword(password.value);
    if (isValid) {
      learningService.setAdminToken(password.value);
      isAuthenticated.value = true;
      await fetchLearningEntries();
    } else {
      authError.value = "密码错误，请重试";
    }
  } catch (error) {
    authError.value = "认证失败，请稍后重试";
  } finally {
    authenticating.value = false;
  }
};

// 获取学习记录
const fetchLearningEntries = async () => {
  loading.value = true;
  try {
    const response = await learningService.getAllLearningEntries();
    if (response.success && response.data) {
      learningEntries.value = response.data;
    }
  } catch (error) {
    console.error("Failed to fetch learning entries:", error);
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN");
};

// 获取语言颜色
const getLanguageColor = (language: string) => {
  const colors: Record<string, string> = {
    JavaScript: "#f7df1e",
    TypeScript: "#3178c6",
    Python: "#3776ab",
    Java: "#ed8b00",
    "C++": "#00599c",
    "C#": "#239120",
    Go: "#00add8",
    Rust: "#000000",
    PHP: "#777bb4",
    Ruby: "#cc342d",
    Swift: "#fa7343",
    Kotlin: "#7f52ff",
    Vue: "#4fc08d",
    React: "#61dafb",
    Angular: "#dd0031",
    "Node.js": "#339933",
    HTML: "#e34f26",
    CSS: "#1572b6",
    SCSS: "#cf649a",
    SQL: "#336791",
    MongoDB: "#47a248",
    Docker: "#2496ed",
    Git: "#f05032",
    Linux: "#fcc624",
    AWS: "#ff9900",
    Azure: "#0078d4",
  };
  return colors[language] || "#6c757d";
};

// 重置表单
const resetForm = () => {
  formData.date = "";
  formData.title = "";
  formData.language = "";
  formData.duration = "";
  formData.content = "";
};

// 关闭模态框
const closeModal = () => {
  showAddForm.value = false;
  showEditForm.value = false;
  editingEntry.value = null;
  resetForm();
};

// 编辑条目
const editEntry = (entry: LearningEntry) => {
  editingEntry.value = entry;
  formData.date = entry.date;
  formData.title = entry.title;
  formData.language = entry.language;
  formData.duration = entry.duration;
  formData.content = entry.content;
  showEditForm.value = true;
};

// 提交表单
const submitForm = async () => {
  submitting.value = true;
  try {
    if (showEditForm.value && editingEntry.value) {
      // 更新
      const response = await learningService.updateLearningEntry(editingEntry.value.id, formData);
      if (response.success) {
        await fetchLearningEntries();
        closeModal();
      }
    } else {
      // 添加
      const response = await learningService.addLearningEntry(formData);
      if (response.success) {
        await fetchLearningEntries();
        closeModal();
      }
    }
  } catch (error) {
    console.error("Failed to submit form:", error);
  } finally {
    submitting.value = false;
  }
};

// 删除条目
const deleteEntry = (id: string) => {
  deletingId.value = id;
  showDeleteConfirm.value = true;
};

// 确认删除
const confirmDelete = async () => {
  deleting.value = true;
  try {
    const response = await learningService.deleteLearningEntry(deletingId.value);
    if (response.success) {
      await fetchLearningEntries();
      showDeleteConfirm.value = false;
    }
  } catch (error) {
    console.error("Failed to delete entry:", error);
  } finally {
    deleting.value = false;
  }
};

// 检查认证状态
onMounted(() => {
  const token = learningService.getAdminToken();
  if (token) {
    isAuthenticated.value = true;
    fetchLearningEntries();
  } else {
    loading.value = false;
  }
});
</script>

<style scoped lang="scss">
.learning-admin {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  color: white;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .admin-title {
    font-size: 2.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;

    .icon {
      font-size: 2.5rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .admin-nav-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
    }

    .btn-icon {
      font-size: 1.2rem;
    }
  }

  .add-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-icon {
      font-size: 1.2rem;
    }
  }
}

.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 100%;

    h2 {
      color: #2c3e50;
      margin-bottom: 1rem;
      font-size: 1.8rem;
    }

    p {
      color: #7f8c8d;
      margin-bottom: 1.5rem;
    }

    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .auth-input {
        padding: 0.75rem;
        border: 2px solid #ecf0f1;
        border-radius: 10px;
        font-size: 1rem;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: #667eea;
        }
      }

      .auth-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem;
        border-radius: 10px;
        font-size: 1rem;
        cursor: pointer;
        transition: opacity 0.3s ease;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .auth-error {
      color: #e74c3c;
      margin-top: 1rem;
      font-size: 0.9rem;
    }
  }
}

.admin-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.admin-content-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ecf0f1;

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: flex-end;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #7f8c8d;

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center;
  color: #7f8c8d;
  padding: 4rem 2rem;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #2c3e50;
  }

  p {
    font-size: 1.1rem;
  }
}

.entries-table {
  .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #2c3e50;

    @media (max-width: 768px) {
      display: none;
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 0.5rem;
      padding: 1rem;
      border: 1px solid #ecf0f1;
      border-radius: 10px;
      margin-bottom: 1rem;
    }

    &:hover {
      background: #f8f9fa;
    }
  }

  .table-cell {
    color: #2c3e50;

    @media (max-width: 768px) {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &::before {
        content: attr(data-label);
        font-weight: 600;
        color: #7f8c8d;
      }
    }

    &.actions {
      display: flex;
      gap: 0.5rem;

      @media (max-width: 768px) {
        justify-content: flex-end;

        &::before {
          display: none;
        }
      }

      .edit-btn,
      .delete-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.85rem;
        transition: all 0.3s ease;
      }

      .edit-btn {
        background: #3498db;
        color: white;

        &:hover {
          background: #2980b9;
        }
      }

      .delete-btn {
        background: #e74c3c;
        color: white;

        &:hover {
          background: #c0392b;
        }
      }
    }
  }

  .language-tag {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;

  &.delete-modal {
    max-width: 400px;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #ecf0f1;

    h2 {
      color: #2c3e50;
      margin: 0;
      font-size: 1.5rem;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #7f8c8d;
      padding: 0.5rem;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background: #ecf0f1;
        color: #2c3e50;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    p {
      color: #7f8c8d;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.entry-form {
  padding: 1.5rem;

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #2c3e50;
      font-weight: 600;
    }

    .form-input,
    .form-textarea {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #ecf0f1;
      border-radius: 10px;
      font-size: 1rem;
      transition: border-color 0.3s ease;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 120px;
      font-family: inherit;
    }
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;

    .cancel-btn,
    .submit-btn,
    .delete-btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      font-size: 1rem;
      transition: all 0.3s ease;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .cancel-btn {
      background: #ecf0f1;
      color: #7f8c8d;

      &:hover:not(:disabled) {
        background: #d5dbdb;
      }
    }

    .submit-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover:not(:disabled) {
        opacity: 0.9;
      }
    }

    .delete-btn {
      background: #e74c3c;
      color: white;

      &:hover:not(:disabled) {
        background: #c0392b;
      }
    }
  }
}
</style>

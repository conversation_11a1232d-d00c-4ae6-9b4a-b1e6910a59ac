<template>
  <div class="learning-admin">
    <!-- 页面标题 -->
    <div class="admin-header">
      <h1 class="admin-title">
        <span class="icon">⚙️</span>
        学习记录管理
      </h1>
    </div>

    <!-- 认证检查 -->
    <div v-if="!isAuthenticated" class="auth-container">
      <div class="auth-card">
        <h2>管理员认证</h2>
        <p>请输入管理员密码以访问学习记录管理功能</p>
        <form @submit.prevent="authenticate" class="auth-form">
          <input
            v-model="password"
            type="password"
            placeholder="管理员密码"
            class="auth-input"
            required
          />
          <button type="submit" class="auth-btn" :disabled="authenticating">
            {{ authenticating ? "验证中..." : "登录" }}
          </button>
        </form>
        <p v-if="authError" class="auth-error">{{ authError }}</p>
      </div>
    </div>

    <!-- 管理界面 -->
    <div v-else class="admin-content">
      <!-- 管理界面头部 -->
      <div class="admin-content-header">
        <div class="header-actions">
          <RouterLink to="/admin" class="admin-nav-btn">
            <span class="btn-icon">🔖</span>
            书签管理
          </RouterLink>
          <button @click="showAddForm = true" class="add-btn">
            <span class="btn-icon">➕</span>
            添加学习记录
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载学习记录中...</p>
      </div>

      <!-- 学习记录列表 -->
      <div v-else class="entries-management">
        <div v-if="learningEntries.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>暂无学习记录</h3>
          <p>点击上方按钮添加第一条学习记录</p>
        </div>

        <div v-else class="entries-table">
          <div class="table-header">
            <div class="header-cell">日期</div>
            <div class="header-cell">标题</div>
            <div class="header-cell">语言</div>
            <div class="header-cell">时长</div>
            <div class="header-cell">操作</div>
          </div>

          <div v-for="entry in learningEntries" :key="entry.id" class="table-row">
            <div class="table-cell">{{ formatDate(entry.date) }}</div>
            <div class="table-cell">{{ entry.title }}</div>
            <div class="table-cell">
              <span
                class="language-tag"
                :style="{ backgroundColor: getLanguageColor(entry.language) }"
              >
                {{ entry.language }}
              </span>
            </div>
            <div class="table-cell">{{ entry.duration }}</div>
            <div class="table-cell actions">
              <button @click="editEntry(entry)" class="edit-btn">编辑</button>
              <button @click="deleteEntry(entry.id)" class="delete-btn">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑表单模态框 -->
    <div v-if="showAddForm || showEditForm" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ showEditForm ? "编辑学习记录" : "添加学习记录" }}</h2>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>

        <form @submit.prevent="submitForm" class="entry-form">
          <div class="form-group">
            <label for="date">学习日期</label>
            <input id="date" v-model="formData.date" type="date" class="form-input" required />
          </div>

          <div class="form-group">
            <label for="title">学习标题</label>
            <input
              id="title"
              v-model="formData.title"
              type="text"
              class="form-input"
              placeholder="例如：Vue 3 组合式 API 学习"
              required
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="language">编程语言</label>
              <select id="language" v-model="formData.language" class="form-input" required>
                <option value="">请选择语言</option>
                <option v-for="lang in programmingLanguages" :key="lang" :value="lang">
                  {{ lang }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="duration">学习时长</label>
              <input
                id="duration"
                v-model="formData.duration"
                type="text"
                class="form-input"
                placeholder="例如：2小时30分钟"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label for="content">学习内容</label>
            <textarea
              id="content"
              v-model="formData.content"
              class="form-textarea"
              placeholder="详细描述今天学习的内容、遇到的问题、解决方案等..."
              rows="6"
              required
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="button" @click="closeModal" class="cancel-btn">取消</button>
            <button type="submit" class="submit-btn" :disabled="submitting">
              {{ submitting ? "保存中..." : showEditForm ? "更新" : "添加" }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteConfirm" class="modal-overlay" @click="showDeleteConfirm = false">
      <div class="modal-content delete-modal" @click.stop>
        <div class="modal-header">
          <h2>确认删除</h2>
        </div>
        <div class="modal-body">
          <p>确定要删除这条学习记录吗？此操作无法撤销。</p>
        </div>
        <div class="form-actions">
          <button @click="showDeleteConfirm = false" class="cancel-btn">取消</button>
          <button @click="confirmDelete" class="delete-btn" :disabled="deleting">
            {{ deleting ? "删除中..." : "确认删除" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { RouterLink } from "vue-router";
import {
  learningService,
  bookmarkService,
  type LearningEntry,
  type LearningEntryInput,
} from "@/services/bookmarkService";

// 认证相关
const isAuthenticated = ref(false);
const password = ref("");
const authenticating = ref(false);
const authError = ref("");

// 数据相关
const learningEntries = ref<LearningEntry[]>([]);
const loading = ref(true);

// 表单相关
const showAddForm = ref(false);
const showEditForm = ref(false);
const submitting = ref(false);
const editingEntry = ref<LearningEntry | null>(null);

// 删除相关
const showDeleteConfirm = ref(false);
const deleting = ref(false);
const deletingId = ref("");

// 表单数据
const formData = reactive<LearningEntryInput>({
  date: "",
  title: "",
  language: "",
  duration: "",
  content: "",
});

// 编程语言选项
const programmingLanguages = [
  "JavaScript",
  "TypeScript",
  "Python",
  "Java",
  "C++",
  "C#",
  "Go",
  "Rust",
  "PHP",
  "Ruby",
  "Swift",
  "Kotlin",
  "Vue",
  "React",
  "Angular",
  "Node.js",
  "HTML",
  "CSS",
  "SCSS",
  "SQL",
  "MongoDB",
  "Docker",
  "Git",
  "Linux",
  "AWS",
  "Azure",
];

// 认证
const authenticate = async () => {
  authenticating.value = true;
  authError.value = "";

  try {
    const isValid = await bookmarkService.verifyAdminPassword(password.value);
    if (isValid) {
      learningService.setAdminToken(password.value);
      isAuthenticated.value = true;
      await fetchLearningEntries();
    } else {
      authError.value = "密码错误，请重试";
    }
  } catch (error) {
    authError.value = "认证失败，请稍后重试";
  } finally {
    authenticating.value = false;
  }
};

// 获取学习记录
const fetchLearningEntries = async () => {
  loading.value = true;
  try {
    const response = await learningService.getAllLearningEntries();
    if (response.success && response.data) {
      learningEntries.value = response.data;
    }
  } catch (error) {
    console.error("Failed to fetch learning entries:", error);
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN");
};

// 获取语言颜色
const getLanguageColor = (language: string) => {
  const colors: Record<string, string> = {
    JavaScript: "#f7df1e",
    TypeScript: "#3178c6",
    Python: "#3776ab",
    Java: "#ed8b00",
    "C++": "#00599c",
    "C#": "#239120",
    Go: "#00add8",
    Rust: "#000000",
    PHP: "#777bb4",
    Ruby: "#cc342d",
    Swift: "#fa7343",
    Kotlin: "#7f52ff",
    Vue: "#4fc08d",
    React: "#61dafb",
    Angular: "#dd0031",
    "Node.js": "#339933",
    HTML: "#e34f26",
    CSS: "#1572b6",
    SCSS: "#cf649a",
    SQL: "#336791",
    MongoDB: "#47a248",
    Docker: "#2496ed",
    Git: "#f05032",
    Linux: "#fcc624",
    AWS: "#ff9900",
    Azure: "#0078d4",
  };
  return colors[language] || "#6c757d";
};

// 重置表单
const resetForm = () => {
  formData.date = "";
  formData.title = "";
  formData.language = "";
  formData.duration = "";
  formData.content = "";
};

// 关闭模态框
const closeModal = () => {
  showAddForm.value = false;
  showEditForm.value = false;
  editingEntry.value = null;
  resetForm();
};

// 编辑条目
const editEntry = (entry: LearningEntry) => {
  editingEntry.value = entry;
  formData.date = entry.date;
  formData.title = entry.title;
  formData.language = entry.language;
  formData.duration = entry.duration;
  formData.content = entry.content;
  showEditForm.value = true;
};

// 提交表单
const submitForm = async () => {
  submitting.value = true;
  try {
    if (showEditForm.value && editingEntry.value) {
      // 更新
      const response = await learningService.updateLearningEntry(editingEntry.value.id, formData);
      if (response.success) {
        await fetchLearningEntries();
        closeModal();
      }
    } else {
      // 添加
      const response = await learningService.addLearningEntry(formData);
      if (response.success) {
        await fetchLearningEntries();
        closeModal();
      }
    }
  } catch (error) {
    console.error("Failed to submit form:", error);
  } finally {
    submitting.value = false;
  }
};

// 删除条目
const deleteEntry = (id: string) => {
  deletingId.value = id;
  showDeleteConfirm.value = true;
};

// 确认删除
const confirmDelete = async () => {
  deleting.value = true;
  try {
    const response = await learningService.deleteLearningEntry(deletingId.value);
    if (response.success) {
      await fetchLearningEntries();
      showDeleteConfirm.value = false;
    }
  } catch (error) {
    console.error("Failed to delete entry:", error);
  } finally {
    deleting.value = false;
  }
};

// 检查认证状态
onMounted(() => {
  const token = learningService.getAdminToken();
  if (token) {
    isAuthenticated.value = true;
    fetchLearningEntries();
  } else {
    loading.value = false;
  }
});
</script>

<style scoped lang="scss">
.learning-admin {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--background-color) 0%, #0f0f1a 100%);
  padding: 8rem 2rem 4rem;
  position: relative;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 51, 102, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  @media (max-width: 768px) {
    padding: 6rem 1rem 2rem;
  }
}

.admin-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;

  .admin-title {
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .icon {
      font-size: 3.5rem;
      filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));
    }

    @media (max-width: 768px) {
      font-size: 2.5rem;
      flex-direction: column;
      gap: 0.5rem;

      .icon {
        font-size: 2.5rem;
      }
    }
  }
}

.admin-nav-btn {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  border: 2px solid rgba(255, 255, 255, 0.1);
  padding: 0.75rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .btn-icon {
    font-size: 1.2rem;
  }
}

.add-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
  }

  .btn-icon {
    font-size: 1.2rem;
  }
}

.admin-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;

  .admin-title {
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .icon {
      font-size: 3.5rem;
      filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));
    }

    @media (max-width: 768px) {
      font-size: 2.5rem;
      flex-direction: column;
      gap: 0.5rem;

      .icon {
        font-size: 2.5rem;
      }
    }
  }
}

.admin-nav-btn {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    border: 2px solid rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 15px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-icon {
      font-size: 1.2rem;
    }
  }

  .add-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 15px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
    }

    .btn-icon {
      font-size: 1.2rem;
    }
  }
}

.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;

  .auth-card {
    background: rgba(34, 40, 49, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 3rem;
    border-radius: 25px;
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.05);
    text-align: center;
    max-width: 450px;
    width: 100%;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    }

    h2 {
      color: var(--text-color);
      margin-bottom: 1rem;
      font-size: 2rem;
      font-weight: 600;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    p {
      color: rgba(232, 232, 240, 0.7);
      margin-bottom: 2rem;
      font-size: 1rem;
      line-height: 1.6;
    }

    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .auth-input {
        padding: 1rem 1.5rem;
        background: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        font-size: 1rem;
        color: var(--text-color);
        transition: all 0.3s ease;

        &::placeholder {
          color: rgba(232, 232, 240, 0.5);
        }

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
        }
      }

      .auth-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }

    .auth-error {
      color: var(--accent-color);
      margin-top: 1rem;
      font-size: 0.9rem;
      padding: 0.75rem;
      background: rgba(255, 51, 102, 0.1);
      border: 1px solid rgba(255, 51, 102, 0.2);
      border-radius: 10px;
    }
  }
}

.admin-content {
  background: rgba(34, 40, 49, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 2.5rem;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  }
}

.admin-content-header {
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: flex-end;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: rgba(232, 232, 240, 0.7);

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .auth-input {
        padding: 1rem 1.5rem;
        background: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        font-size: 1rem;
        color: var(--text-color);
        transition: all 0.3s ease;

        &::placeholder {
          color: rgba(232, 232, 240, 0.5);
        }

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
        }
      }

      .auth-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }

    .auth-error {
      color: var(--accent-color);
      margin-top: 1rem;
      font-size: 0.9rem;
      padding: 0.75rem;
      background: rgba(255, 51, 102, 0.1);
      border: 1px solid rgba(255, 51, 102, 0.2);
      border-radius: 10px;
    }
  }
}

.admin-content {
  background: rgba(34, 40, 49, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 2.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  }
}

.admin-content-header {
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: flex-end;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #7f8c8d;

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center;
  color: rgba(232, 232, 240, 0.7);
  padding: 4rem 2rem;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.3));
  }

  h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.8;
  }
}

.entries-table {
  .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-color);
    backdrop-filter: blur(10px);

    @media (max-width: 768px) {
      display: none;
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    align-items: center;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 0.75rem;
      padding: 1.5rem;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
  }

  .table-cell {
    color: var(--text-color);

    @media (max-width: 768px) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);

      &:last-child {
        border-bottom: none;
      }

      &::before {
        content: attr(data-label);
        font-weight: 600;
        color: rgba(232, 232, 240, 0.7);
        font-size: 0.9rem;
      }
    }

    &.actions {
      display: flex;
      gap: 0.75rem;

      @media (max-width: 768px) {
        justify-content: flex-end;

        &::before {
          display: none;
        }
      }

      .edit-btn,
      .delete-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }
      }

      .edit-btn {
        background: linear-gradient(135deg, var(--primary-color), #2980b9);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
      }

      .delete-btn {
        background: linear-gradient(135deg, var(--accent-color), #c0392b);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
      }
    }
  }

  .language-tag {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: rgba(34, 40, 49, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  animation: slideUp 0.3s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  }

  &.delete-modal {
    max-width: 450px;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h2 {
      color: var(--text-color);
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .close-btn {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      font-size: 1.5rem;
      cursor: pointer;
      color: rgba(232, 232, 240, 0.7);
      padding: 0.5rem;
      border-radius: 50%;
      transition: all 0.3s ease;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-color);
        transform: rotate(90deg);
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    p {
      color: #7f8c8d;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.entry-form {
  padding: 2rem;

  .form-group {
    margin-bottom: 2rem;

    label {
      display: block;
      margin-bottom: 0.75rem;
      color: var(--text-color);
      font-weight: 600;
      font-size: 1rem;
    }

    .form-input,
    .form-textarea {
      width: 100%;
      padding: 1rem 1.5rem;
      background: rgba(255, 255, 255, 0.05);
      border: 2px solid rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      font-size: 1rem;
      color: var(--text-color);
      transition: all 0.3s ease;
      box-sizing: border-box;

      &::placeholder {
        color: rgba(232, 232, 240, 0.5);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 140px;
      font-family: inherit;
      line-height: 1.6;
    }
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .cancel-btn,
    .submit-btn,
    .delete-btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 15px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover::before {
        left: 100%;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }

    .cancel-btn {
      background: rgba(255, 255, 255, 0.05);
      color: rgba(232, 232, 240, 0.7);
      border: 2px solid rgba(255, 255, 255, 0.1);

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-color);
        transform: translateY(-2px);
      }
    }

    .submit-btn {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
      color: white;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
      }
    }

    .delete-btn {
      background: linear-gradient(135deg, var(--accent-color), #c0392b);
      color: white;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
      }
    }
  }
}
</style>

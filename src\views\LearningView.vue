<template>
  <div class="learning-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <span class="icon">📚</span>
        每日学习记录
      </h1>
      <p class="page-subtitle">记录每一天的学习成长轨迹</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载学习记录中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p class="error-message">{{ error }}</p>
      <button @click="fetchLearningEntries" class="retry-btn">重试</button>
    </div>

    <!-- 学习记录列表 -->
    <div v-else class="learning-entries">
      <div v-if="learningEntries.length === 0" class="empty-state">
        <div class="empty-icon">📖</div>
        <h3>暂无学习记录</h3>
        <p>还没有添加任何学习记录，开始记录你的学习之旅吧！</p>
      </div>

      <div v-else class="entries-grid">
        <div
          v-for="entry in learningEntries"
          :key="entry.id"
          class="entry-card"
          :class="{ 'featured': isRecentEntry(entry.date) }"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="date-badge">
              <span class="date-text">{{ formatDate(entry.date) }}</span>
              <span v-if="isRecentEntry(entry.date)" class="recent-badge">最新</span>
            </div>
            <div class="language-tag" :style="{ backgroundColor: getLanguageColor(entry.language) }">
              {{ entry.language }}
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <h3 class="entry-title">{{ entry.title }}</h3>
            <div class="duration-info">
              <span class="duration-icon">⏱️</span>
              <span class="duration-text">学习时长: {{ entry.duration }}</span>
            </div>
            <div class="content-preview">
              <p>{{ entry.content }}</p>
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer">
            <span class="created-time">
              记录于 {{ formatDateTime(entry.createdAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { learningService, type LearningEntry } from '@/services/bookmarkService'

// 响应式数据
const learningEntries = ref<LearningEntry[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

// 获取学习记录
const fetchLearningEntries = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await learningService.getAllLearningEntries()
    if (response.success && response.data) {
      learningEntries.value = response.data
    } else {
      error.value = response.error || '获取学习记录失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
    console.error('Failed to fetch learning entries:', err)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 判断是否为最近的记录（7天内）
const isRecentEntry = (dateString: string) => {
  const entryDate = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - entryDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 7
}

// 根据编程语言获取颜色
const getLanguageColor = (language: string) => {
  const colors: Record<string, string> = {
    'JavaScript': '#f7df1e',
    'TypeScript': '#3178c6',
    'Python': '#3776ab',
    'Java': '#ed8b00',
    'C++': '#00599c',
    'C#': '#239120',
    'Go': '#00add8',
    'Rust': '#000000',
    'PHP': '#777bb4',
    'Ruby': '#cc342d',
    'Swift': '#fa7343',
    'Kotlin': '#7f52ff',
    'Vue': '#4fc08d',
    'React': '#61dafb',
    'Angular': '#dd0031',
    'Node.js': '#339933',
    'HTML': '#e34f26',
    'CSS': '#1572b6',
    'SCSS': '#cf649a',
    'SQL': '#336791',
    'MongoDB': '#47a248',
    'Docker': '#2496ed',
    'Git': '#f05032',
    'Linux': '#fcc624',
    'AWS': '#ff9900',
    'Azure': '#0078d4'
  }
  return colors[language] || '#6c757d'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchLearningEntries()
})
</script>

<style scoped lang="scss">
.learning-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;

  .page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    .icon {
      font-size: 3.5rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
      flex-direction: column;
      gap: 0.5rem;

      .icon {
        font-size: 2.5rem;
      }
    }
  }

  .page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;

    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: white;
  text-align: center;

  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .error-message {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .retry-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  color: white;
  padding: 4rem 2rem;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.8;
  }
}

.entries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.entry-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  &.featured {
    border-color: #ffd700;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;

  .date-badge {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .date-text {
      font-size: 0.9rem;
      color: #666;
      font-weight: 500;
    }

    .recent-badge {
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 15px;
      font-size: 0.75rem;
      font-weight: 600;
      align-self: flex-start;
    }
  }

  .language-tag {
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.card-content {
  .entry-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.4;
  }

  .duration-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #7f8c8d;
    font-size: 0.9rem;

    .duration-icon {
      font-size: 1rem;
    }
  }

  .content-preview {
    p {
      color: #34495e;
      line-height: 1.6;
      font-size: 0.95rem;
      margin: 0;
    }
  }
}

.card-footer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #ecf0f1;

  .created-time {
    color: #95a5a6;
    font-size: 0.8rem;
  }
}
</style>
